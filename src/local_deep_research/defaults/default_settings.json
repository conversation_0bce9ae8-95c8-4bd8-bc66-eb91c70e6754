{"app.debug": {"category": "app_interface", "description": "Enable debug mode for the web application", "editable": true, "max_value": null, "min_value": null, "name": "Debug Mode", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "app.enable_notifications": {"category": "app_interface", "description": "Enable browser notifications for research events", "editable": true, "max_value": null, "min_value": null, "name": "Enable Notifications", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "app.enable_web": {"category": "app_interface", "description": "Enable the web server", "editable": true, "max_value": null, "min_value": null, "name": "Enable Web Server", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "app.host": {"category": "app_interface", "description": "Host address to bind the web server", "editable": true, "max_value": null, "min_value": null, "name": "Web Host", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": "0.0.0.0", "visible": true}, "app.port": {"category": "app_interface", "description": "Port for the web server", "editable": true, "max_value": 65535.0, "min_value": 1.0, "name": "Web Port", "options": null, "step": null, "type": "APP", "ui_element": "number", "value": 5000, "visible": true}, "app.theme": {"category": "app_interface", "description": "User interface theme", "editable": true, "max_value": null, "min_value": null, "name": "UI Theme", "options": [{"label": "Dark", "value": "dark"}, {"label": "Light", "value": "light"}, {"label": "System Default", "value": "system"}], "step": null, "type": "APP", "ui_element": "select", "value": "dark", "visible": true}, "app.web_interface": {"category": "app_interface", "description": "Enable the web interface", "editable": true, "max_value": null, "min_value": null, "name": "Web Interface", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "llm.llamacpp_f16_kv": {"category": null, "description": "Setting for llm.llamacpp_f16_kv", "editable": true, "max_value": null, "min_value": null, "name": "Llamacpp F16 Kv", "options": null, "step": null, "type": "LLM", "ui_element": "checkbox", "value": true, "visible": true}, "llm.llamacpp_model_path": {"category": null, "description": "Setting for llm.llamacpp_model_path", "editable": true, "max_value": null, "min_value": null, "name": "Llamacpp Model Path", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "", "visible": true}, "llm.llamacpp_n_batch": {"category": null, "description": "Setting for llm.llamacpp_n_batch", "editable": true, "max_value": null, "min_value": 1, "name": "Llamacpp N Batch", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 512, "visible": true}, "llm.llamacpp_n_gpu_layers": {"category": null, "description": "Setting for llm.llamacpp_n_gpu_layers", "editable": true, "max_value": null, "min_value": 0, "name": "Llamacpp N Gpu Layers", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 1, "visible": true}, "llm.lmstudio.url": {"category": null, "description": "URL of the LMStudio endpoint.", "editable": true, "max_value": null, "min_value": null, "name": "Lmstudio Url", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "http://localhost:1234", "visible": true}, "llm.max_tokens": {"category": "llm_parameters", "description": "Maximum number of tokens in model responses", "editable": true, "max_value": 4096.0, "min_value": 100.0, "name": "<PERSON>", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 30000, "visible": true}, "llm.context_window_size": {"category": "llm_parameters", "description": "Maximum context window size in tokens for the LLM", "editable": true, "max_value": 20000000.0, "min_value": 512.0, "name": "Context Window Size", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 128000, "visible": true}, "llm.supports_max_tokens": {"category": "llm_parameters", "description": "Whether the LLM API supports the 'max_tokens' option.", "editable": true, "max_value": null, "min_value": null, "name": "Supports '<PERSON>'", "options": null, "step": null, "type": "LLM", "ui_element": "checkbox", "value": true, "visible": true}, "llm.model": {"category": "llm_general", "description": "Language model to use for research and analysis", "editable": true, "max_value": null, "min_value": null, "name": "LLM Model", "options": [{"label": "GPT-4o (OpenAI)", "value": "gpt-4o"}, {"label": "GPT-3.5 Turbo (OpenAI)", "value": "gpt-3.5-turbo"}, {"label": "Llama 3 (Meta)", "value": "llama3"}, {"label": "<PERSON><PERSON><PERSON> (Mistral AI)", "value": "mistral"}, {"label": "<PERSON><PERSON> (Mistral AI)", "value": "mixtral"}], "step": null, "type": "LLM", "ui_element": "select", "value": "gemma3:12b", "visible": true}, "llm.openai_endpoint.url": {"category": null, "description": "URL of the OpenAI-compatible endpoint.", "editable": true, "max_value": null, "min_value": null, "name": "Openai Endpoint Url", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "https://openrouter.ai/api/v1", "visible": true}, "llm.ollama.url": {"category": null, "description": "URL of the Ollama endpoint.", "editable": true, "max_value": null, "min_value": null, "name": "Ollama Endpoint Url", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "http://localhost:11434", "visible": true}, "llm.provider": {"category": "llm_general", "description": "Service provider for the language model", "editable": true, "max_value": null, "min_value": null, "name": "LLM Provider", "options": [{"label": "OpenAI API", "value": "openai"}, {"label": "<PERSON><PERSON><PERSON> (Local)", "value": "ollama"}, {"label": "LM Studio (Local)", "value": "lmstudio"}, {"label": "vLLM (Local)", "value": "vllm"}, {"label": "Custom OpenAI-compatible API", "value": "openai_endpoint"}], "step": null, "type": "LLM", "ui_element": "select", "value": "OLLAMA", "visible": true}, "llm.temperature": {"category": "llm_parameters", "description": "Controls randomness in model outputs (0.0 - 1.0)", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Temperature", "options": null, "step": 0.05, "type": "LLM", "ui_element": "slider", "value": 0.7, "visible": true}, "llm.anthropic.api_key": {"category": "llm_general", "description": "API key to use for the Anthropic provider.", "editable": true, "max_value": null, "min_value": null, "name": "Anthropic API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "ANTHROPIC_API_KEY", "visible": true}, "llm.openai.api_key": {"category": "llm_general", "description": "API key to use for the OpenAI provider.", "editable": true, "max_value": null, "min_value": null, "name": "OpenAI API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "OPENAI_API_KEY", "visible": true}, "llm.openai_endpoint.api_key": {"category": "llm_general", "description": "API key to use for the OpenAI-compatible endpoint provider.", "editable": true, "max_value": null, "min_value": null, "name": "OpenAI Endpoint API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "OPENAI_ENDPOINT_API_KEY", "visible": true}, "report.detailed_citations": {"category": "report_parameters", "description": "Include detailed citations in reports", "editable": true, "max_value": null, "min_value": null, "name": "Detailed Citations", "options": null, "step": null, "type": "REPORT", "ui_element": "checkbox", "value": true, "visible": true}, "report.enable_fact_checking": {"category": "report_parameters", "description": "Enable fact checking for report contents", "editable": true, "max_value": null, "min_value": null, "name": "Enable Fact Checking", "options": null, "step": null, "type": "REPORT", "ui_element": "checkbox", "value": true, "visible": true}, "report.searches_per_section": {"category": "report_parameters", "description": "Number of searches to run per report section", "editable": true, "max_value": 5.0, "min_value": 1.0, "name": "Searches Per Section", "options": null, "step": null, "type": "REPORT", "ui_element": "number", "value": 2, "visible": true}, "search.iterations": {"category": null, "description": "Setting for search.iterations", "editable": true, "max_value": null, "min_value": 1, "name": "Search Iterations", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 2, "visible": true}, "search.max_filtered_results": {"category": "search_parameters", "description": "Maximum number of search results to display after relevance filtering", "editable": true, "max_value": 100, "min_value": 1, "name": "<PERSON> Filtered Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.max_results": {"category": "search_parameters", "description": "Maximum number of search results to retrieve", "editable": true, "max_value": 50.0, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 50, "visible": true}, "search.final_max_results": {"category": "search_parameters", "description": "Maximum number of search results to include in the final report.", "editable": true, "max_value": 200.0, "min_value": 1, "name": "Final Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 100, "visible": true}, "search.quality_check_urls": {"category": null, "description": "Setting for search.quality_check_urls", "editable": true, "max_value": null, "min_value": null, "name": "Quality Check Urls", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.questions_per_iteration": {"category": "search_parameters", "description": "Number of questions to generate per research cycle", "editable": true, "max_value": 10.0, "min_value": 1.0, "name": "Questions Per Iteration", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 2, "visible": true}, "search.region": {"category": "search_parameters", "description": "Geographic region for search results", "editable": true, "max_value": null, "min_value": null, "name": "Search Region", "options": [{"label": "United States", "value": "us"}, {"label": "United Kingdom", "value": "uk"}, {"label": "France", "value": "fr"}, {"label": "Germany", "value": "de"}, {"label": "Japan", "value": "jp"}, {"label": "No Region (Worldwide)", "value": "wt-wt"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "us", "visible": true}, "search.safe_search": {"category": "search_parameters", "description": "Enable safe search filtering", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.search_language": {"category": "search_parameters", "description": "Language for search results", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": [{"label": "English", "value": "English"}, {"label": "French", "value": "French"}, {"label": "German", "value": "German"}, {"label": "Spanish", "value": "Spanish"}, {"label": "Italian", "value": "Italian"}, {"label": "Japanese", "value": "Japanese"}, {"label": "Chinese", "value": "Chinese"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "English", "visible": true}, "search.searches_per_section": {"category": "search_parameters", "description": "Number of searches to run per report section", "editable": true, "max_value": 5.0, "min_value": 1.0, "name": "Searches Per Section", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 2, "visible": true}, "search.skip_relevance_filter": {"category": "search_parameters", "description": "Skip filtering search results for relevance", "editable": true, "max_value": null, "min_value": null, "name": "Skip Relevance Filter", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.snippets_only": {"category": "search_parameters", "description": "Only retrieve snippets instead of full search results", "editable": true, "max_value": null, "min_value": null, "name": "Snippets Only", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.time_period": {"category": "search_parameters", "description": "Time period for search results", "editable": true, "max_value": null, "min_value": null, "name": "Time Period", "options": [{"label": "Past 24 hours", "value": "d"}, {"label": "Past week", "value": "w"}, {"label": "Past month", "value": "m"}, {"label": "Past year", "value": "y"}, {"label": "All time", "value": "all"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "y", "visible": true}, "search.tool": {"category": "search_general", "description": "Web search engine to use for research", "editable": true, "max_value": null, "min_value": null, "name": "Search Engine", "options": [{"label": "Auto (Default)", "value": "auto"}, {"label": "SerpAPI (Google)", "value": "serp<PERSON>i"}, {"label": "SearXNG (Self-hosted)", "value": "searxng"}, {"label": "Google Programmable Search Engine", "value": "google_pse"}, {"label": "DuckDuckGo", "value": "duckduck<PERSON>"}, {"label": "Brave", "value": "brave"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "searxng", "visible": true}, "search.engine.auto.display_name": {"category": "auto", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Auto", "visible": false}, "search.engine.auto.description": {"category": "auto", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Attempt to choose the best combination of search engines automatically.", "visible": false}, "search.engine.auto.class_name": {"category": "auto", "description": "Setting for auto.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "MetaSearchEngine", "visible": true}, "search.engine.auto.default_params.max_engines_to_try": {"category": "auto", "description": "Setting for auto.default_params.max_engines_to_try", "editable": true, "max_value": null, "min_value": 1, "name": "Max Engines To Try", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 3, "visible": true}, "search.engine.auto.default_params.use_api_key_services": {"category": "auto", "description": "Setting for auto.default_params.use_api_key_services", "editable": true, "max_value": null, "min_value": null, "name": "Use Api Key Services", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.auto.module_path": {"category": "auto", "description": "Setting for auto.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.meta_search_engine", "visible": true}, "search.engine.auto.reliability": {"category": "auto", "description": "Setting for auto.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.85, "visible": true}, "search.engine.auto.requires_api_key": {"category": "auto", "description": "Setting for auto.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.auto.requires_llm": {"category": "auto", "description": "Setting for auto.requires_llm", "editable": true, "max_value": null, "min_value": null, "name": "Requires Llm", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.auto.strengths": {"category": "auto", "description": "Setting for auto.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["intelligent engine selection", "adaptable to query type", "fallback capabilities"], "visible": true}, "search.engine.auto.weaknesses": {"category": "auto", "description": "Setting for auto.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["slightly slower due to LLM analysis"], "visible": true}, "search.engine.web.brave.display_name": {"category": "brave", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Brave", "visible": false}, "search.engine.web.brave.description": {"category": "brave", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the web using the Brave search engine.", "visible": false}, "search.engine.web.brave.api_key": {"category": "brave", "description": "The Brave API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "BRAVE_API_KEY", "visible": true}, "search.engine.web.brave.class_name": {"category": "brave", "description": "Setting for brave.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "BraveSearchEngine", "visible": true}, "search.engine.web.brave.default_params.region": {"category": "brave", "description": "Setting for brave.default_params.region", "editable": true, "max_value": null, "min_value": null, "name": "Region", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "US", "visible": true}, "search.engine.web.brave.default_params.safe_search": {"category": "brave", "description": "Setting for brave.default_params.safe_search", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.default_params.search_language": {"category": "brave", "description": "Setting for brave.default_params.search_language", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.brave.default_params.time_period": {"category": "brave", "description": "Setting for brave.default_params.time_period", "editable": true, "max_value": null, "min_value": null, "name": "Time Period", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "y", "visible": true}, "search.engine.web.brave.full_search_class": {"category": "brave", "description": "Setting for brave.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSearchResults", "visible": true}, "search.engine.web.brave.full_search_module": {"category": "brave", "description": "Setting for brave.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "local_deep_research.web_search_engines.engines.full_search", "visible": true}, "search.engine.web.brave.module_path": {"category": "brave", "description": "Setting for brave.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_brave", "visible": true}, "search.engine.web.brave.reliability": {"category": "brave", "description": "Setting for brave.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.7, "visible": true}, "search.engine.web.brave.requires_api_key": {"category": "brave", "description": "Setting for brave.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.strengths": {"category": "brave", "description": "Setting for brave.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["privacy-focused web search", "product information", "reviews", "recent content", "news", "broad coverage"], "visible": true}, "search.engine.web.brave.supports_full_search": {"category": "brave", "description": "Setting for brave.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.weaknesses": {"category": "brave", "description": "Setting for brave.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["requires API key with usage limits", "smaller index than Google"], "visible": true}, "general.enable_fact_checking": {"category": null, "description": "Setting for general.enable_fact_checking", "editable": true, "max_value": null, "min_value": null, "name": "Enable Fact Checking", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": false, "visible": true}, "general.knowledge_accumulation": {"category": null, "description": "Setting for general.knowledge_accumulation", "editable": true, "max_value": null, "min_value": null, "name": "Knowledge Accumulation", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": "ITERATION", "visible": true}, "general.knowledge_accumulation_context_limit": {"category": null, "description": "Setting for general.knowledge_accumulation_context_limit", "editable": true, "max_value": null, "min_value": null, "name": "Knowledge Accumulation Context Limit", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": 2000000, "visible": true}, "general.output_dir": {"category": null, "description": "Setting for general.output_dir", "editable": true, "max_value": null, "min_value": null, "name": "Output Dir", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": "research_outputs", "visible": true}, "general.max_research_time_minutes": {"category": "general_settings", "description": "Default maximum time in minutes a research process can run before automatic termination", "editable": true, "max_value": 120, "min_value": 5, "name": "De<PERSON>ult Max Research Time (minutes)", "options": null, "step": 5, "type": "APP", "ui_element": "number", "value": 30, "visible": true}, "general.quick_research_time_minutes": {"category": "general_settings", "description": "Maximum time in minutes a quick research process can run before automatic termination", "editable": true, "max_value": 60, "min_value": 5, "name": "Quick Research Time Limit (minutes)", "options": null, "step": 5, "type": "APP", "ui_element": "number", "value": 15, "visible": true}, "general.detailed_research_time_minutes": {"category": "general_settings", "description": "Maximum time in minutes a detailed research process can run before automatic termination", "editable": true, "max_value": 120, "min_value": 10, "name": "Detailed Research Time Limit (minutes)", "options": null, "step": 5, "type": "APP", "ui_element": "number", "value": 45, "visible": true}, "search.engine.web.google_pse.display_name": {"category": "google_pse", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Google PSE", "visible": false}, "search.engine.web.google_pse.description": {"category": "google_pse", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the web using Google's Programmable Search Engine API.", "visible": false}, "search.engine.web.google_pse.api_key": {"category": "google_pse", "description": "The Google PSE API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "GOOGLE_PSE_API_KEY", "visible": true}, "search.engine.web.google_pse.class_name": {"category": "google_pse", "description": "Setting for google_pse.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "GooglePSESearchEngine", "visible": true}, "search.engine.web.google_pse.default_params.region": {"category": "google_pse", "description": "Setting for google_pse.default_params.region", "editable": true, "max_value": null, "min_value": null, "name": "Region", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "us", "visible": true}, "search.engine.web.google_pse.default_params.safe_search": {"category": "google_pse", "description": "Setting for google_pse.default_params.safe_search", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.google_pse.default_params.search_language": {"category": "google_pse", "description": "Setting for google_pse.default_params.search_language", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.google_pse.full_search_class": {"category": "google_pse", "description": "Setting for google_pse.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSearchResults", "visible": true}, "search.engine.web.google_pse.full_search_module": {"category": "google_pse", "description": "Setting for google_pse.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.full_search", "visible": true}, "search.engine.web.google_pse.module_path": {"category": "google_pse", "description": "Setting for google_pse.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_google_pse", "visible": true}, "search.engine.web.google_pse.reliability": {"category": "google_pse", "description": "Setting for google_pse.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.9, "visible": true}, "search.engine.web.google_pse.requires_api_key": {"category": "google_pse", "description": "Setting for google_pse.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.google_pse.strengths": {"category": "google_pse", "description": "Setting for google_pse.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["custom search scope", "high-quality results", "domain-specific search", "configurable search experience", "control over search index"], "visible": true}, "search.engine.web.google_pse.supports_full_search": {"category": "google_pse", "description": "Setting for google_pse.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.google_pse.weaknesses": {"category": "google_pse", "description": "Setting for google_pse.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["requires API key with usage limits", "limited to 10,000 queries/day on free tier", "requires search engine configuration in Google Control Panel"], "visible": true}, "search.engine.DEFAULT_SEARCH_ENGINE": {"category": "search_general", "description": "Default search engine to use when no specific engine is selected", "editable": true, "max_value": null, "min_value": null, "name": "Default Search Engine", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "searxng", "visible": true}, "search.engine.web.searxng.display_name": {"category": "searxng", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SearXNG (Locally-hosted)", "visible": false}, "search.engine.web.searxng.description": {"category": "searxng", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "A locally-hosted meta-search engine.", "visible": false}, "search.engine.web.searxng.class_name": {"category": "searxng", "description": "Setting for searxng.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SearXNGSearchEngine", "visible": true}, "search.engine.web.searxng.default_params.instance_url": {"category": "searxng", "description": "The SearXNG API endpoint URL.", "editable": true, "max_value": null, "min_value": null, "name": "Endpoint URL", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "http://localhost:8080", "visible": true}, "search.engine.web.searxng.default_params.categories": {"category": "searxng", "description": "Setting for searxng.default_params.categories", "editable": true, "max_value": null, "min_value": null, "name": "Categories", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["general"], "visible": true}, "search.engine.web.searxng.default_params.delay_between_requests": {"category": "searxng", "description": "Setting for searxng.default_params.delay_between_requests", "editable": true, "max_value": null, "min_value": 0, "name": "Delay Between Requests", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 0, "visible": true}, "search.engine.web.searxng.default_params.include_full_content": {"category": "searxng", "description": "Setting for searxng.default_params.include_full_content", "editable": true, "max_value": null, "min_value": null, "name": "Include Full Content", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.searxng.default_params.language": {"category": "searxng", "description": "Setting for searxng.default_params.language", "editable": true, "max_value": null, "min_value": null, "name": "Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "en", "visible": true}, "search.engine.web.searxng.default_params.max_results": {"category": "searxng", "description": "Setting for searxng.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 15, "visible": true}, "search.engine.web.searxng.default_params.safe_search": {"category": "searxng", "description": "Configure the safe search level", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": [{"label": "Off", "value": "OFF"}, {"label": "Moderate", "value": "MODERATE"}, {"label": "Strict", "value": "STRICT"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "OFF", "visible": true}, "search.engine.web.searxng.full_search_class": {"category": "searxng", "description": "Setting for searxng.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSearchResults", "visible": true}, "search.engine.web.searxng.full_search_module": {"category": "searxng", "description": "Setting for searxng.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.full_search", "visible": true}, "search.engine.web.searxng.module_path": {"category": "searxng", "description": "Setting for searxng.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_searxng", "visible": true}, "search.engine.web.searxng.reliability": {"category": "searxng", "description": "Setting for searxng.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 1.0, "visible": true}, "search.engine.web.searxng.requires_api_key": {"category": "searxng", "description": "Setting for searxng.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.searxng.strengths": {"category": "searxng", "description": "Setting for searxng.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["comprehensive general information", "current events and news", "technical documentation", "factual queries", "historical information", "consumer products", "educational content", "multi-source aggregation", "real-time results", "combined results from major search engines"], "visible": true}, "search.engine.web.searxng.supports_full_search": {"category": "searxng", "description": "Setting for searxng.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.searxng.weaknesses": {"category": "searxng", "description": "Setting for searxng.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["requires self-hosting", "depends on other search engines", "may be rate limited by underlying engines"], "visible": true}, "search.engine.web.serpapi.display_name": {"category": "serp<PERSON>i", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SerpApi", "visible": false}, "search.engine.web.serpapi.description": {"category": "serp<PERSON>i", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the web with Google's search API.", "visible": false}, "search.engine.web.serpapi.api_key": {"category": "serp<PERSON>i", "description": "The Serp API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "SERP_API_KEY", "visible": true}, "search.engine.web.serpapi.class_name": {"category": "serp<PERSON>i", "description": "Setting for serpapi.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SerpAPISearchEngine", "visible": true}, "search.engine.web.serpapi.default_params.region": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.region", "editable": true, "max_value": null, "min_value": null, "name": "Region", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "us", "visible": true}, "search.engine.web.serpapi.default_params.safe_search": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.safe_search", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.serpapi.default_params.search_language": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.search_language", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.serpapi.default_params.time_period": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.time_period", "editable": true, "max_value": null, "min_value": null, "name": "Time Period", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "y", "visible": true}, "search.engine.web.serpapi.full_search_class": {"category": "serp<PERSON>i", "description": "Setting for serpapi.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSerpAPISearchResults", "visible": true}, "search.engine.web.serpapi.full_search_module": {"category": "serp<PERSON>i", "description": "Setting for serpapi.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "local_deep_research.web_search_engines.engines.full_serp_search_results_old", "visible": true}, "search.engine.web.serpapi.module_path": {"category": "serp<PERSON>i", "description": "Setting for serpapi.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_serpapi", "visible": true}, "search.engine.web.serpapi.reliability": {"category": "serp<PERSON>i", "description": "Setting for serpapi.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.6, "visible": true}, "search.engine.web.serpapi.requires_api_key": {"category": "serp<PERSON>i", "description": "Setting for serpapi.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.serpapi.strengths": {"category": "serp<PERSON>i", "description": "Setting for serpapi.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["comprehensive web search", "product information", "reviews", "recent content", "news", "broad coverage"], "visible": true}, "search.engine.web.serpapi.supports_full_search": {"category": "serp<PERSON>i", "description": "Setting for serpapi.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.serpapi.weaknesses": {"category": "serp<PERSON>i", "description": "Setting for serpapi.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ["requires API key with usage limits", "not specialized for academic content"], "visible": true}, "web.debug": {"category": null, "description": "Setting for web.debug", "editable": true, "max_value": null, "min_value": null, "name": "Debug", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": true, "visible": true}, "web.host": {"category": null, "description": "Setting for web.host", "editable": true, "max_value": null, "min_value": null, "name": "Host", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "0.0.0.0", "visible": true}, "web.port": {"category": null, "description": "Setting for web.port", "editable": true, "max_value": null, "min_value": null, "name": "Port", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": 5000, "visible": true}, "search.cross_engine_max_results": {"category": "search_parameters", "description": "Maximum number of search results to keep after cross-engine filtering. When results from multiple search engines are combined, this limits how many total results are displayed. Higher values show more comprehensive results.", "editable": true, "max_value": 1000, "min_value": 1, "name": "Cross-Engine Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 100, "visible": true}, "search.engine.web.searxng.use_in_auto_search": {"category": "searxng", "description": "Include SearXNG in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.use_in_auto_search": {"category": "brave", "description": "Include Brave search in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.google_pse.use_in_auto_search": {"category": "google_pse", "description": "Include Google PSE in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.serpapi.use_in_auto_search": {"category": "serp<PERSON>i", "description": "Include SerpAPI in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "llm.llamacpp_connection_mode": {"category": "llm_parameters", "description": "Connection mode for LlamaCpp: 'local' for direct model loading or 'http' for using a remote server", "editable": true, "max_value": null, "min_value": null, "name": "LlamaCpp Connection Mode", "options": [{"label": "Local Model", "value": "local"}, {"label": "HTTP Server", "value": "http"}], "step": null, "type": "LLM", "ui_element": "select", "value": "local", "visible": true}, "llm.llamacpp_server_url": {"category": "llm_parameters", "description": "URL of the LlamaCpp HTTP server (only used when connection mode is 'http')", "editable": true, "max_value": null, "min_value": null, "name": "LlamaCpp Server URL", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "http://localhost:8000", "visible": true}}